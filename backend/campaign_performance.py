"""
Campaign Performance Backend Script
Aggregates all campaign performance functionality including performance analysis,
prediction models, metrics calculation, and mail performance analysis.

This script combines functionality from:
- src/openengage/core/campaign_predictor.py
- src/openengage/core/mail_performance_analyzer.py

Inputs:
- campaign_file: Path to campaign data CSV file
- performance_file: Path to historical performance data CSV file
- campaign_data: DataFrame or dict containing campaign information
- user_data: User engagement and behavior data

Outputs:
- Performance metrics and predictions
- Mail performance analysis results
"""
import os
import pandas as pd
import numpy as np
import logging
import re
import json
from datetime import datetime
from pathlib import Path
from glob import glob
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import OneHotEncoder, StandardScaler
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CampaignPredictor:
    """
    A class to predict campaign performance metrics based on historical data.
    """
    
    def __init__(self):
        """Initialize the CampaignPredictor."""
        self.models = {
            'open_rate': None,
            'click_rate': None,
            'unsub_rate': None
        }
        self.feature_columns = None
        self.preprocessor = None
        self.model_trained = False
        self.training_metrics = {}
        self.logger = self._setup_logger()

    def _setup_logger(self):
        """Set up logger for the predictor"""
        logger = logging.getLogger("openengage.campaign_predictor")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _extract_features_from_campaign(self, campaign_df):
        """
        Extract features from campaign data for prediction.
        
        Args:
            campaign_df (pd.DataFrame): DataFrame containing campaign data
            
        Returns:
            pd.DataFrame: DataFrame with extracted features
        """
        features = pd.DataFrame()
        
        # Extract subject line features
        if 'Subject' in campaign_df.columns:
            # Subject line length
            features['subject_length'] = campaign_df['Subject'].str.len()
            
            # Check for personalization in subject
            features['subject_has_personalization'] = campaign_df['Subject'].str.contains(r'\{.*?\}', regex=True).astype(int)
            
            # Check for urgency words
            urgency_words = ['urgent', 'limited', 'hurry', 'now', 'today', 'expires']
            features['subject_has_urgency'] = campaign_df['Subject'].str.lower().str.contains('|'.join(urgency_words), regex=True).astype(int)
            
        # Extract email content features
        if 'Mail_Content' in campaign_df.columns:
            # Email content length
            features['content_length'] = campaign_df['Mail_Content'].str.len()
            
            # Count links in content
            features['link_count'] = campaign_df['Mail_Content'].str.count(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+')
            
            # Check for personalization in content
            features['content_has_personalization'] = campaign_df['Mail_Content'].str.contains(r'\{.*?\}', regex=True).astype(int)
            
        # Extract timing features
        if 'Send_Time' in campaign_df.columns:
            send_time = pd.to_datetime(campaign_df['Send_Time'])
            features['send_hour'] = send_time.dt.hour
            features['send_day_of_week'] = send_time.dt.dayofweek
            features['send_month'] = send_time.dt.month
            
        # Extract user stage features
        if 'user_stage' in campaign_df.columns:
            features['user_stage'] = campaign_df['user_stage']
            
        # Extract product features
        if 'Matched_Product' in campaign_df.columns:
            features['product'] = campaign_df['Matched_Product']
        elif 'last_product_sent' in campaign_df.columns:
            features['product'] = campaign_df['last_product_sent']
            
        return features
    
    def _prepare_training_data(self, performance_df):
        """
        Prepare training data from performance data.
        
        Args:
            performance_df (pd.DataFrame): DataFrame containing performance data
            
        Returns:
            tuple: X (features) and y (targets) for training
        """
        # Extract features
        features = self._extract_features_from_campaign(performance_df)
        
        # Prepare target variables
        targets = pd.DataFrame()
        
        # Calculate open rate
        if 'Open_Time' in performance_df.columns:
            targets['open_rate'] = performance_df['Open_Time'].notna().astype(int)
            
        # Calculate click rate
        if 'Click_Time' in performance_df.columns:
            targets['click_rate'] = performance_df['Click_Time'].notna().astype(int)
            
        # Calculate unsubscribe rate
        if 'Unsubscribe_Time' in performance_df.columns:
            targets['unsub_rate'] = performance_df['Unsubscribe_Time'].notna().astype(int)
        
        # Handle categorical features
        categorical_features = ['user_stage', 'product']
        numerical_features = [col for col in features.columns if col not in categorical_features]
        
        # Create preprocessor
        self.preprocessor = ColumnTransformer(
            transformers=[
                ('num', StandardScaler(), numerical_features),
                ('cat', OneHotEncoder(handle_unknown='ignore'), categorical_features)
            ]
        )
        
        # Fit preprocessor and transform features
        X = self.preprocessor.fit_transform(features)
        
        # Store feature columns for later use
        self.feature_columns = features.columns.tolist()
        
        return X, targets

    def train_models(self, performance_file=None):
        """
        Train prediction models using historical performance data.
        
        Args:
            performance_file (str, optional): Path to performance data file.
                If None, will look for the most recent performance file.
                
        Returns:
            bool: True if training was successful, False otherwise
        """
        try:
            # Find the performance file if not provided
            if performance_file is None:
                performance_files = glob("data/mail_performance/combined/all_performance_*.csv")
                if not performance_files:
                    logger.warning("No performance data files found")
                    return False
                performance_file = max(performance_files)  # Get the most recent file
                
            logger.info(f"Training models using performance data from: {performance_file}")
            
            # Load performance data
            performance_df = pd.read_csv(performance_file)
            
            # Check if we have enough data for training
            if len(performance_df) < 50:
                logger.warning(f"Not enough data for training ({len(performance_df)} records). Need at least 50.")
                return False
            
            # Prepare training data
            X, targets = self._prepare_training_data(performance_df)
            
            # Train models for each target
            for target in ['open_rate', 'click_rate', 'unsub_rate']:
                if target in targets.columns:
                    # Split data
                    X_train, X_test, y_train, y_test = train_test_split(
                        X, targets[target], test_size=0.2, random_state=42
                    )
                    
                    # Create and train model
                    model = RandomForestRegressor(n_estimators=100, random_state=42)
                    model.fit(X_train, y_train)
                    
                    # Evaluate model
                    y_pred = model.predict(X_test)
                    mse = mean_squared_error(y_test, y_pred)
                    r2 = r2_score(y_test, y_pred)
                    
                    # Store model and metrics
                    self.models[target] = model
                    self.training_metrics[target] = {
                        'mse': mse,
                        'r2': r2,
                        'baseline': targets[target].mean()
                    }
                    
                    logger.info(f"Trained {target} model - R²: {r2:.3f}, MSE: {mse:.3f}")
                else:
                    logger.warning(f"Target column '{target}' not found in performance data")
            
            self.model_trained = True
            logger.info("Model training completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error training models: {str(e)}")
            return False

    def predict_campaign_performance(self, campaign_file=None):
        """
        Predict performance metrics for a campaign.
        
        Args:
            campaign_file (str, optional): Path to campaign data file.
                If None, will look for the most recent campaign file.
                
        Returns:
            dict: Predicted performance metrics
        """
        try:
            # Check if models are trained
            if not self.model_trained:
                logger.warning("Models are not trained. Training now...")
                if not self.train_models():
                    return {
                        'success': False,
                        'error': 'Failed to train models'
                    }
            
            # Find the campaign file if not provided
            if campaign_file is None:
                campaign_files = glob("data/campaign_results/*.csv")
                if not campaign_files:
                    logger.warning("No campaign files found")
                    return {
                        'success': False,
                        'error': 'No campaign files found'
                    }
                campaign_file = max(campaign_files)  # Get the most recent file
                
            logger.info(f"Predicting performance for campaign: {campaign_file}")
            
            # Load campaign data
            campaign_df = pd.read_csv(campaign_file)
            
            # Extract features
            features = self._extract_features_from_campaign(campaign_df)
            
            # Ensure features are in the same order as during training
            features = features[self.feature_columns]
            
            # Preprocess features
            X = self.preprocessor.transform(features)
            
            # Make predictions
            predictions = {}
            for target, model in self.models.items():
                if model is not None:
                    # Predict for each row
                    row_predictions = model.predict(X)
                    
                    # Calculate average prediction
                    avg_prediction = np.mean(row_predictions) * 100  # Convert to percentage
                    
                    predictions[target] = avg_prediction
                    
                    logger.info(f"Predicted {target}: {avg_prediction:.2f}%")
                else:
                    logger.warning(f"No model available for {target}")
            
            return {
                'success': True,
                'predictions': predictions,
                'training_metrics': self.training_metrics,
                'campaign_file': campaign_file,
                'campaign_size': len(campaign_df)
            }
            
        except Exception as e:
            logger.error(f"Error predicting campaign performance: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }


class MailPerformanceAnalyzer:
    """
    A class to analyze email campaign performance and track engagement metrics.
    """

    def __init__(self):
        """Initialize the MailPerformanceAnalyzer."""
        self.year_month = datetime.now().strftime("%Y%m")
        # Create directories for storing performance data if they don't exist
        self.data_dir = Path("data/mail_performance")
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # Create subdirectories for different types of data
        self.openers_dir = self.data_dir / "openers"
        self.clickers_dir = self.data_dir / "clickers"
        self.unsub_dir = self.data_dir / "unsubscribers"
        self.performance_dir = self.data_dir / "combined"

        self.openers_dir.mkdir(exist_ok=True)
        self.clickers_dir.mkdir(exist_ok=True)
        self.unsub_dir.mkdir(exist_ok=True)
        self.performance_dir.mkdir(exist_ok=True)

    def save_performance_data(self, openers_df, clickers_df, unsub_df, performance_df):
        """
        Save performance data to separate files.

        Args:
            openers_df (pd.DataFrame): DataFrame containing opener data
            clickers_df (pd.DataFrame): DataFrame containing clicker data
            unsub_df (pd.DataFrame): DataFrame containing unsubscriber data
            performance_df (pd.DataFrame): DataFrame containing combined performance data
        """
        try:
            # Save openers data
            if not openers_df.empty:
                openers_file = self.openers_dir / "all_openers.csv"
                openers_df.to_csv(openers_file, index=False)
                logger.info(f"Saved {len(openers_df)} opener records to {openers_file}")

            # Save clickers data
            if not clickers_df.empty:
                clickers_file = self.clickers_dir / "all_clickers.csv"
                clickers_df.to_csv(clickers_file, index=False)
                logger.info(f"Saved {len(clickers_df)} clicker records to {clickers_file}")

            # Save unsubscribers data
            if not unsub_df.empty:
                unsub_file = self.unsub_dir / "all_unsub.csv"
                unsub_df.to_csv(unsub_file, index=False)
                logger.info(f"Saved {len(unsub_df)} unsubscriber records to {unsub_file}")

            # Save combined performance data
            if not performance_df.empty:
                performance_file = self.performance_dir / f"all_performance_{self.year_month}.csv"
                performance_df.to_csv(performance_file, index=False)
                logger.info(f"Saved {len(performance_df)} performance records to {performance_file}")

        except Exception as e:
            logger.error(f"Error saving performance data: {str(e)}")

    def load_previous_data(self):
        """
        Load previous performance data from saved files.

        Returns:
            tuple: (openers_df, clickers_df, unsub_df, performance_df) DataFrames with previous data
        """
        openers_file = self.openers_dir / "all_openers.csv"
        clickers_file = self.clickers_dir / "all_clickers.csv"
        unsub_file = self.unsub_dir / "all_unsub.csv"
        performance_file = self.performance_dir / f"all_performance_{self.year_month}.csv"

        openers_df = pd.DataFrame()
        clickers_df = pd.DataFrame()
        unsub_df = pd.DataFrame()
        performance_df = pd.DataFrame()

        if openers_file.exists():
            try:
                openers_df = pd.read_csv(openers_file)
                logger.info(f"Loaded {len(openers_df)} previous opener records")
            except Exception as e:
                logger.error(f"Error loading openers data: {str(e)}")

        if clickers_file.exists():
            try:
                clickers_df = pd.read_csv(clickers_file)
                logger.info(f"Loaded {len(clickers_df)} previous clicker records")
            except Exception as e:
                logger.error(f"Error loading clickers data: {str(e)}")

        if unsub_file.exists():
            try:
                unsub_df = pd.read_csv(unsub_file)
                logger.info(f"Loaded {len(unsub_df)} previous unsubscriber records")
            except Exception as e:
                logger.error(f"Error loading unsubscribers data: {str(e)}")

        if performance_file.exists():
            try:
                performance_df = pd.read_csv(performance_file)
                logger.info(f"Loaded {len(performance_df)} previous performance records")
            except Exception as e:
                logger.error(f"Error loading performance data: {str(e)}")

        return openers_df, clickers_df, unsub_df, performance_df

    def calculate_performance_metrics(self, performance_file=None):
        """
        Calculate comprehensive performance metrics from performance data.

        Args:
            performance_file (str, optional): Path to the performance data file.
                If None, will use the latest combined performance file.

        Returns:
            dict: Performance metrics and visualization data
        """
        # Find performance file if not provided
        if performance_file is None:
            # Try to find the latest combined performance file
            performance_file = Path(f"data/mail_performance/combined/all_performance_{self.year_month}.csv")

        if not performance_file.exists():
            # Fall back to the monthly performance file
            performance_file = f"data/mail_performance/mail_performance_{self.year_month}.csv"

            if not os.path.exists(performance_file):
                # Try to find any performance file
                performance_files = glob("data/mail_performance/mail_performance_*.csv")
                if performance_files:
                    performance_file = max(performance_files)  # Get the most recent file
                else:
                    logger.warning("No performance data files found")
                    return {
                        "total_emails": 0,
                        "open_count": 0,
                        "open_rate": 0,
                        "click_count": 0,
                        "click_rate": 0,
                        "click_to_open_rate": 0,
                        "unsub_count": 0,
                        "unsub_rate": 0
                    }

        try:
            df = pd.read_csv(performance_file)

            # Calculate metrics
            total_emails = len(df)

            # Check if required columns exist
            open_count = 0
            if 'Open_Time' in df.columns:
                open_count = df['Open_Time'].notna().sum()
            else:
                logger.warning("'Open_Time' column not found in performance data")

            open_rate = (open_count / total_emails) * 100 if total_emails > 0 else 0

            click_count = 0
            if 'Click_Time' in df.columns:
                click_count = df['Click_Time'].notna().sum()
            else:
                logger.warning("'Click_Time' column not found in performance data")

            click_rate = (click_count / total_emails) * 100 if total_emails > 0 else 0
            click_to_open_rate = (click_count / open_count) * 100 if open_count > 0 else 0

            unsub_count = 0
            if 'Unsubscribe_Time' in df.columns:
                unsub_count = df['Unsubscribe_Time'].notna().sum()
            else:
                logger.warning("'Unsubscribe_Time' column not found in performance data")

            unsub_rate = (unsub_count / total_emails) * 100 if total_emails > 0 else 0

            # Prepare visualization data
            visualization_data = []
            try:
                if 'Send_Time' in df.columns:
                    # Convert Send_Time to datetime and extract date
                    df['Send_Time'] = pd.to_datetime(df['Send_Time'], errors='coerce')
                    df['date'] = df['Send_Time'].dt.date

                    # Group by date and calculate daily metrics
                    viz_data = df.groupby('date').agg({
                        'user_email': 'count',  # Total sent
                        'Open_Time': lambda x: x.notna().sum(),  # Total opened
                        'Click_Time': lambda x: x.notna().sum()  # Total clicked
                    }).reset_index()

                    viz_data.columns = ['date', 'total_sent', 'total_opened', 'total_clicked']

                    # Calculate rates
                    viz_data['open_rate'] = (viz_data['total_opened'] / viz_data['total_sent']) * 100
                    viz_data['click_rate'] = (viz_data['total_clicked'] / viz_data['total_sent']) * 100

                    # Convert date to string for serialization
                    viz_data['date'] = viz_data['date'].astype(str)

                    # Convert to dict for returning
                    visualization_data = viz_data.to_dict('records')
            except Exception as e:
                logger.error(f"Error preparing visualization data: {str(e)}")

            return {
                "total_emails": total_emails,
                "open_count": open_count,
                "open_rate": open_rate,
                "click_count": click_count,
                "click_rate": click_rate,
                "click_to_open_rate": click_to_open_rate,
                "unsub_count": unsub_count,
                "unsub_rate": unsub_rate,
                "visualization_data": visualization_data
            }

        except Exception as e:
            logger.error(f"Error calculating performance metrics: {str(e)}")
            return {
                "total_emails": 0,
                "open_count": 0,
                "open_rate": 0,
                "click_count": 0,
                "click_rate": 0,
                "click_to_open_rate": 0,
                "unsub_count": 0,
                "unsub_rate": 0,
                "error": str(e)
            }
