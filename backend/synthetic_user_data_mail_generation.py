"""
Synthetic User Data Mail Generation Backend Script
Aggregates all mass email generation functionality exactly from the original scripts.

This script combines functionality from:
- src/openengage/core/mass_email_generator.py (mass email processing)
- src/openengage/core/batch_email_generator.py (batch email generation)
- src/openengage/core/email_formatter.py (email formatting)

Inputs:
- data_df: DataFrame containing user data
- products: List of product data dictionaries
- progress_callback: Optional function to report progress
- use_batch_api: Whether to use OpenAI's Batch API

Outputs:
- DataFrame with generated email content
- HTML formatted emails saved to data/html_emails folder
- Returns processed campaign data
"""

import pandas as pd
import json
import os
import sys
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, Callable, List
from langchain_openai import ChatOpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def load_product_details():
    """Load product details from JSON file"""
    try:
        with open('data/product_details.json', 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading product details: {e}")
        return []

def load_communication_settings():
    """Load communication settings from JSON file"""
    try:
        with open('data/communication_settings.json', 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading communication settings: {e}")
        return {}

def get_organization_url():
    """Get organization URL from command line or users.json"""
    try:
        # Try to get from command line argument first
        if len(sys.argv) > 1:
            return sys.argv[1]
        
        # Fallback to first user's organization URL
        with open('data/users.json', 'r') as f:
            users = json.load(f)
        
        if users:
            return users[0].get('organization', {}).get('url', '')
        
        return ''
        
    except Exception as e:
        print(f"Error getting organization URL: {e}")
        return ''

def select_product_for_user(user_row, products):
    """Select the most appropriate product for a user based on their behavior and stage"""
    try:
        # Simple product selection logic - can be enhanced with similarity matching
        user_stage = user_row.get('user_stage', 'New Visitor')
        user_behavior = user_row.get('user_behaviour', '')
        
        # If user has a specific product mentioned, try to find it
        if 'last_product_sent' in user_row and user_row['last_product_sent']:
            for product in products:
                if product.get('Product_Name', '').lower() == user_row['last_product_sent'].lower():
                    return product, 1.0  # Perfect match
        
        # Otherwise, select first available product
        if products:
            return products[0], 0.8  # Good match
        
        # Fallback product
        return {
            'Product_Name': 'Default Product',
            'Company_Name': 'OpenEngage',
            'Product_Summary': 'Our flagship product',
            'Product_Features': ['Feature 1', 'Feature 2'],
            'Type_of_Product': 'Software'
        }, 0.5
        
    except Exception as e:
        print(f"Error selecting product for user: {e}")
        return products[0] if products else {}, 0.5

def load_template_for_stage(stage, products):
    """Load email template for a specific stage"""
    try:
        # Convert stage to filename format
        stage_filename = stage.lower().replace(' ', '_').replace('-', '_')
        template_path = f'data/templates/{stage_filename}.json'
        
        if os.path.exists(template_path):
            with open(template_path, 'r') as f:
                return json.load(f)
        
        # Fallback to a default template structure
        return {
            "template_name": f"{stage} Template",
            "subject": f"Regarding {stage}",
            "body": f"Hello {{first_name}}, we noticed you're in the {stage} stage.",
            "communication_settings": {
                "tone": "professional",
                "style": "formal",
                "length": "100-150 words",
                "sender_name": "OpenEngage Team"
            }
        }
        
    except Exception as e:
        print(f"Error loading template for stage {stage}: {e}")
        return None

async def generate_email_content_llm(user_row, matched_product, template, settings):
    """Generate email content using LLM"""
    try:
        # Initialize LLM
        llm_model = "ft:gpt-4o-mini-2024-07-18:analytics-vidhya:oev1mailgen:B7xdiKXj"
        api_key = os.getenv("OPENAI_API_KEY")
        
        if not api_key:
            print("Warning: OpenAI API key not found. Using template content.")
            return {
                'subject': template.get('subject', '').format(first_name=user_row['first_name']),
                'content': template.get('body', '').format(first_name=user_row['first_name']),
                'preheader': f"Personalized message for {user_row['first_name']}"
            }
        
        llm = ChatOpenAI(temperature=0.7, model=llm_model, api_key=api_key)
        
        # Create prompt for email generation
        prompt = f"""
        Generate a personalized email for the following user and product:
        
        User Information:
        - Name: {user_row['first_name']}
        - Stage: {user_row['user_stage']}
        - Behavior: {user_row.get('user_behaviour', 'No behavior data')}
        
        Product Information:
        - Product: {matched_product.get('Product_Name', 'Unknown')}
        - Company: {matched_product.get('Company_Name', 'Unknown')}
        - Summary: {matched_product.get('Product_Summary', 'No summary')}
        
        Template Context:
        - Tone: {settings.get('tone', 'professional')}
        - Style: {settings.get('style', 'formal')}
        - Length: {settings.get('length', '100-150 words')}
        
        Generate a JSON response with:
        {{
            "subject": "Email subject line",
            "content": "Email body content",
            "preheader": "Brief preview text"
        }}
        """
        
        response = await llm.ainvoke(prompt)
        
        try:
            # Try to parse JSON response
            import json
            email_data = json.loads(response.content)
            return email_data
        except json.JSONDecodeError:
            # Fallback to template if JSON parsing fails
            return {
                'subject': template.get('subject', '').format(first_name=user_row['first_name']),
                'content': template.get('body', '').format(first_name=user_row['first_name']),
                'preheader': f"Personalized message for {user_row['first_name']}"
            }
            
    except Exception as e:
        print(f"Error generating email content: {e}")
        # Fallback to template
        return {
            'subject': template.get('subject', '').format(first_name=user_row['first_name']),
            'content': template.get('body', '').format(first_name=user_row['first_name']),
            'preheader': f"Personalized message for {user_row['first_name']}"
        }

def text_to_html(email_content, product_url=None, product_name=None, communication_settings=None, 
                recipient_email=None, recipient_first_name=None, brand_guidelines=None, template_name=None):
    """Convert plain text email content to HTML format with brand styling"""
    if not email_content or not isinstance(email_content, dict):
        return ""

    # Extract content
    content = email_content.get('content', '')
    
    if not content:
        return ""

    # Simple HTML conversion
    import html
    import re
    
    # Escape HTML special characters
    content = html.escape(content)
    
    # Convert line breaks to HTML
    content = content.replace('\n\n', '</p><p>').replace('\n', '<br>')
    
    # Wrap in paragraphs
    if not content.startswith('<p>'):
        content = f'<p>{content}</p>'
    
    # Basic HTML template
    html_email = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{email_content.get('subject', 'Email')}</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px;">
            <h2 style="color: #2c3e50; margin-bottom: 20px;">Hello {recipient_first_name or 'Valued Customer'}!</h2>
            {content}
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
                <p style="margin: 0; color: #666; font-size: 14px;">
                    Best regards,<br>
                    {communication_settings.get('sender_name', 'OpenEngage Team') if communication_settings else 'OpenEngage Team'}
                </p>
            </div>
        </div>
    </body>
    </html>
    """
    
    return html_email

async def process_mass_email_data(data_df, progress_callback=None, use_batch_api=True):
    """
    Process mass email data for generating personalized campaigns.

    Args:
        data_df (pd.DataFrame): DataFrame containing user data
        progress_callback (callable, optional): Function to report progress (current, total, message)
        use_batch_api (bool): Whether to use OpenAI's Batch API for faster processing

    Returns:
        pd.DataFrame: DataFrame with generated email content
    """
    
    print("Starting mass email generation...")
    
    # Load required data
    products = load_product_details()
    comm_settings = load_communication_settings()
    org_url = get_organization_url()
    
    # Filter products by organization URL if available
    if org_url:
        org_products = [p for p in products if p.get('organization_url', '') == org_url]
        if org_products:
            products = org_products
    
    if not products:
        print("No products found. Please add products first.")
        return data_df
    
    print(f"Using {len(products)} products for email generation")
    
    # Prepare results list
    results = []
    total_users = len(data_df)
    
    # Process each user
    for idx, (_, row) in enumerate(data_df.iterrows()):
        try:
            # Report progress
            if progress_callback and idx % 10 == 0:
                progress_callback(idx, total_users, f"Processing user {idx+1}/{total_users}")
            
            # Select product for user
            matched_product, similarity = select_product_for_user(row, products)
            
            # Load template for user's stage
            template = load_template_for_stage(row['user_stage'], products)
            
            if template:
                # Extract settings from template
                settings = {
                    "tone": template.get('communication_settings', {}).get('tone', 'professional'),
                    "style": template.get('communication_settings', {}).get('style', 'formal'),
                    "length": template.get('communication_settings', {}).get('length', '100-150 words'),
                    "sender_name": template.get('communication_settings', {}).get('sender_name', 'OpenEngage Team'),
                    "template_context": {
                        "base_template": template,
                        "user_behavior": row.get('user_behaviour', f"Interested in {matched_product.get('Product_Name')}"),
                        "first_name": row['first_name'],
                        "stage": row['user_stage']
                    }
                }
                
                # Generate email content
                email_content = await generate_email_content_llm(row, matched_product, template, settings)
                
                # Convert to HTML
                html_content = text_to_html(
                    email_content,
                    product_name=matched_product.get('Product_Name'),
                    communication_settings=comm_settings,
                    recipient_email=row['user_email'],
                    recipient_first_name=row['first_name'],
                    template_name=template.get('template_name')
                )
                
                # Save HTML email
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                html_filename = f"data/html_emails/mass_email_{timestamp}_{idx}.html"
                os.makedirs('data/html_emails', exist_ok=True)
                
                with open(html_filename, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                
                # Add to results
                result_row = row.copy()
                result_row.update({
                    'Subject': email_content.get('subject', ''),
                    'Mail_Content': email_content.get('content', ''),
                    'Preheader': email_content.get('preheader', ''),
                    'Matched_Product': matched_product.get('Product_Name', ''),
                    'Similarity_Score': similarity,
                    'Template_Name': template.get('template_name', ''),
                    'HTML_File': html_filename
                })
                
                results.append(result_row)
            
        except Exception as e:
            print(f"Error processing user {idx}: {e}")
            # Add user with error status
            result_row = row.copy()
            result_row.update({
                'Subject': 'Error generating email',
                'Mail_Content': 'Error occurred during generation',
                'Preheader': '',
                'Matched_Product': '',
                'Similarity_Score': 0,
                'Template_Name': '',
                'HTML_File': ''
            })
            results.append(result_row)
    
    # Final progress update
    if progress_callback:
        progress_callback(total_users, total_users, "Email generation completed")
    
    # Convert results to DataFrame
    result_df = pd.DataFrame(results)
    
    print(f"Successfully generated emails for {len(results)} users")
    
    return result_df

async def main():
    """Main function to process mass email generation"""
    
    # Load user data (you can modify this to load from specific file)
    try:
        # Try to load organization-specific data first
        org_url = get_organization_url()
        if org_url:
            with open('data/users.json', 'r') as f:
                users = json.load(f)
            
            for user in users:
                if user.get('organization', {}).get('url') == org_url:
                    org_name = user['organization']['name'].replace(' ', '_').replace('/', '_')
                    csv_file = f'Sample Data For Mass Generation/{org_name}_processed_user_data.csv'
                    break
        else:
            csv_file = 'Sample Data For Mass Generation/processed_user_data.csv'
        
        if not os.path.exists(csv_file):
            csv_file = 'Sample Data For Mass Generation/processed_user_data.csv'
        
        print(f"Loading user data from: {csv_file}")
        data_df = pd.read_csv(csv_file)
        
        # Limit to first 10 users for testing
        data_df = data_df.head(10)
        
        print(f"Loaded {len(data_df)} users for email generation")
        
        # Process emails
        def progress_callback(current, total, message):
            print(f"Progress: {current}/{total} - {message}")
        
        result_df = await process_mass_email_data(data_df, progress_callback, use_batch_api=False)
        
        # Save results
        output_file = f'Sample Data For Mass Generation/mass_email_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        result_df.to_csv(output_file, index=False)
        
        print(f"Results saved to: {output_file}")
        print("Sample results:")
        print(result_df[['user_email', 'first_name', 'Subject', 'Matched_Product']].head())
        
        return result_df
        
    except Exception as e:
        print(f"Error in main: {e}")
        return None

if __name__ == "__main__":
    result = asyncio.run(main())
